<script setup>
import { computed } from "vue"

// 定义props
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  targetTitle: {
    type: String,
    default: "",
  },
})

// 定义emits
const emit = defineEmits(["confirm", "cancel", "update:show"])

// 创建计算属性来处理v-model
const dialogVisible = computed({
  get: () => props.show,
  set: (value) => {
    emit("update:show", value)
  },
})

// 方法
const handleConfirm = () => {
  emit("confirm")
}

const handleCancel = () => {
  emit("cancel")
}
</script>

<template>
  <!-- 删除确认弹窗 -->
  <VDialog v-model="dialogVisible" max-width="400" persistent>
    <VCard class="delete-confirm-dialog">
      <VCardTitle class="dialog-title">
        <VIcon icon="mdi-alert-circle" color="warning" class="me-2" />
        删除对话
      </VCardTitle>

      <VCardText class="dialog-content">
        <p class="confirm-text">确定要删除这个对话吗"{{ targetTitle }}"？</p>
      </VCardText>

      <VCardActions class="dialog-actions">
        <VSpacer />
        <VBtn variant="text" color="grey" @click="handleCancel"> 取消 </VBtn>
        <VBtn variant="flat" color="error" @click="handleConfirm"> 确定删除 </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>

<style scoped>
/* 删除确认弹窗样式 */
.delete-confirm-dialog {
  background-color: var(--app-bg-primary) !important;
  color: var(--app-text-primary) !important;
}

.dialog-title {
  display: flex;
  align-items: center;
  padding: 20px 24px 16px 24px !important;
  font-size: 18px;
  font-weight: 600;
  color: var(--app-text-primary) !important;
  border-bottom: 1px solid var(--app-border-color);
}

.dialog-content {
  padding: 20px 24px !important;
  text-align: center;
}

.confirm-text {
  font-size: 16px;
  color: var(--app-text-primary);
  margin-bottom: 12px;
  font-weight: 500;
}

.conversation-name {
  font-size: 14px;
  color: var(--v-theme-primary);
  background-color: var(--app-bg-secondary);
  padding: 8px 12px;
  border-radius: 6px;
  margin: 12px 0;
  font-weight: 500;
  word-break: break-all;
}

.warning-text {
  font-size: 12px;
  color: var(--app-text-secondary);
  margin-top: 12px;
  margin-bottom: 0;
  opacity: 0.8;
}

.dialog-actions {
  padding: 16px 24px 20px 24px !important;
  border-top: 1px solid var(--app-border-color);
}

.dialog-actions .v-btn {
  min-width: 80px;
  font-weight: 500;
}

/* 深色主题下的弹窗样式 */
[data-theme="dark"] .delete-confirm-dialog {
  background-color: var(--app-bg-primary) !important;
}

[data-theme="dark"] .dialog-title,
[data-theme="dark"] .confirm-text {
  color: var(--app-text-primary) !important;
}

[data-theme="dark"] .conversation-name {
  background-color: var(--app-bg-secondary) !important;
  color: var(--v-theme-primary) !important;
}
</style>
