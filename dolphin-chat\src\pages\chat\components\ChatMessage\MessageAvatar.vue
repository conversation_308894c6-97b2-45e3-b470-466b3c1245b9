<template>
  <div class="message-avatar">
    <!-- AI头像 -->
    <div v-if="isAI" class="ai-avatar">
      <img :src="IntelligentIcon" alt="AI" class="avatar-icon" />
    </div>

    <!-- 用户头像 -->
    <div v-else class="user-avatar">
      <img v-if="!isImage" :src="AccountCenterIcon" alt="User" class="avatar-icon" />
      <VIcon v-else icon="mdi-image" size="20" color="#666" />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// 导入SVG图标
import IntelligentIcon from '@/assets/logo.webp'
import AccountCenterIcon from '@/assets/icons/Account Center-37.svg'

// 接收props
const props = defineProps({
  messageType: {
    type: String,
    required: true,
  },
})

// 计算属性
const isAI = computed(() => props.messageType === 'ai')
const isUser = computed(() => props.messageType === 'user')
const isImage = computed(() => props.messageType === 'image')
</script>

<style scoped>
.message-avatar {
  flex-shrink: 0;
}

/* AI头像样式 */
.ai-avatar {
  width: 40px;
  height: 40px;
  /* border-radius: 50%;
  background: #f5f5f5; */
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* 用户头像样式 */
.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* SVG图标样式 */
.avatar-icon {
  width: 35px;
  height: 38px;
  object-fit: contain;
}

/* AI头像图标 - 灰色 */
/* .ai-avatar .avatar-icon {
  filter: var(--icon-filter-secondary);
} */

/* 用户头像图标 - 统一灰色风格 */
.user-avatar .avatar-icon {
  filter: var(--icon-filter-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-avatar,
  .user-avatar {
    width: 36px;
    height: 36px;
  }

  .avatar-icon {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 480px) {
  .ai-avatar,
  .user-avatar {
    width: 32px;
    height: 32px;
  }

  .avatar-icon {
    width: 16px;
    height: 16px;
  }
}
</style>
