/**
 * 清理字体设置相关的localStorage数据
 * 在删除字体设置模块后，清理可能残留的本地存储数据
 */

export function cleanupFontSettings() {
  try {
    // 清理字体相关的localStorage项
    const fontKeys = [
      'font-family',
      'font-size', 
      'line-height',
      'dolphin-font-family',
      'dolphin-font-size',
      'dolphin-line-height'
    ]
    
    fontKeys.forEach(key => {
      if (localStorage.getItem(key)) {
        localStorage.removeItem(key)
        console.log(`✅ 已清理字体设置: ${key}`)
      }
    })
    
    // 清理可能的CSS变量设置
    if (typeof document !== 'undefined') {
      const root = document.documentElement
      const fontCSSVars = [
        '--app-font-family-current',
        '--app-font-size-current', 
        '--app-line-height-current'
      ]
      
      fontCSSVars.forEach(varName => {
        root.style.removeProperty(varName)
      })
      
      console.log('✅ 已清理字体CSS变量')
    }
    
    console.log('✅ 字体设置清理完成')
  } catch (error) {
    console.warn('⚠️ 字体设置清理失败:', error)
  }
}

// 自动执行清理（仅在开发环境）
if (process.env.NODE_ENV === 'development') {
  cleanupFontSettings()
}
