import { defineStore } from "pinia"
import { ModelService } from "@/api/model"
import { MessageService } from "@/api/message"

export const useAppStore = defineStore("app", {
  state: () => ({
    // 应用全局状态
    loading: false,
    // 主题相关状态
    theme: "system", // 'light' | 'dark' | 'system'
    actualTheme: "light", // 实际应用的主题 'light' | 'dark'
    systemTheme: "light", // 系统主题 'light' | 'dark'
  }),

  getters: {
    // 获取当前应用的主题
    currentTheme: (state) => {
      return state.theme === "system" ? state.systemTheme : state.theme
    },

    // 获取主题显示名称
    themeDisplayName: (state) => {
      const themeNames = {
        system: "跟随系统",
        light: "浅色主题",
        dark: "深色主题",
      }
      return themeNames[state.theme] || "跟随系统"
    },
  },

  actions: {
    setLoading(loading) {
      this.loading = loading
    },

    // 设置主题
    setTheme(theme) {
      this.theme = theme
      this.updateActualTheme()
      this.saveThemeToStorage()
    },

    // 切换主题（保持原有的 toggleTheme 方法兼容性）
    toggleTheme() {
      const themes = ["light", "dark", "system"]
      const currentIndex = themes.indexOf(this.theme)
      const nextIndex = (currentIndex + 1) % themes.length
      this.setTheme(themes[nextIndex])
    },

    // 设置系统主题
    setSystemTheme(systemTheme) {
      this.systemTheme = systemTheme
      this.updateActualTheme()
    },

    // 更新实际应用的主题
    updateActualTheme() {
      this.actualTheme = this.theme === "system" ? this.systemTheme : this.theme
    },

    // 从本地存储加载主题
    loadThemeFromStorage() {
      const savedTheme = localStorage.getItem("dolphin-theme")
      if (savedTheme && ["light", "dark", "system"].includes(savedTheme)) {
        this.theme = savedTheme
      }
      this.updateActualTheme()
    },

    // 保存主题到本地存储
    saveThemeToStorage() {
      localStorage.setItem("dolphin-theme", this.theme)
    },

    // 初始化主题系统
    initTheme() {
      // 检测系统主题
      this.detectSystemTheme()

      // 加载用户保存的主题偏好
      this.loadThemeFromStorage()

      // 监听系统主题变化
      this.watchSystemTheme()
    },

    // 检测系统主题
    detectSystemTheme() {
      if (typeof window !== "undefined" && window.matchMedia) {
        const prefersDark = window.matchMedia("(prefers-color-scheme: dark)")
        this.systemTheme = prefersDark.matches ? "dark" : "light"
      }
    },

    // 监听系统主题变化
    watchSystemTheme() {
      if (typeof window !== "undefined" && window.matchMedia) {
        const prefersDark = window.matchMedia("(prefers-color-scheme: dark)")

        const handleThemeChange = (e) => {
          this.setSystemTheme(e.matches ? "dark" : "light")
        }

        // 现代浏览器使用 addEventListener
        if (prefersDark.addEventListener) {
          prefersDark.addEventListener("change", handleThemeChange)
        } else {
          // 兼容旧版浏览器
          prefersDark.addListener(handleThemeChange)
        }
      }
    },
  },
})

// 聊天状态管理
export const useChatStore = defineStore("chat", {
  state: () => ({
    // 聊天相关状态
    messages: [],
    currentConversationId: null,
    isLoading: false,
    showWelcome: true,
    // 全局加载弹窗状态
    showGlobalLoading: false,
    globalLoadingText: "正在加载对话数据",
    globalLoadingSubText: "请稍候...",
    globalLoadingProgress: 0,
    showGlobalProgress: false,
    // 照片墙相关状态
    showPhotoWall: false,
    photoWallImages: [], // 存储所有图片消息的引用
    // 图片画布相关状态
    showImageCanvas: false,
    canvasImages: [], // 存储画布中显示的图片
    // API相关状态
    apiError: null,
    connectionStatus: "connected", // connected, disconnected, connecting
    // 流式响应状态
    streamingMessageId: null, // 当前正在流式更新的消息ID
    isStreamingActive: false, // 是否有活跃的流式响应
    // 消息编辑状态
    editingMessageId: null, // 当前正在编辑的消息ID
    editingContent: "", // 编辑中的消息内容
    // 模型选择相关状态
    selectedModel: "gpt-4",
    availableModels: [
      {
        id: "gpt-4",
        name: "GPT-4",
        shortName: "GPT-4",
        description: "GPT-4 模型",
        icon: "mdi-brain",
        color: "primary",
      },
      {
        id: "gpt-3.5-turbo",
        name: "GPT-3.5 Turbo",
        shortName: "GPT-3.5",
        description: "GPT-3.5 Turbo 模型",
        icon: "mdi-lightning-bolt",
        color: "success",
      },
      {
        id: "claude-3",
        name: "Claude-3",
        shortName: "Claude",
        description: "Claude-3 模型",
        icon: "mdi-book-open-variant",
        color: "info",
      },
      {
        id: "gemini-pro",
        name: "Gemini Pro",
        shortName: "Gemini",
        description: "Gemini Pro 模型",
        icon: "mdi-star",
        color: "warning",
      },
    ],
    // 模型加载状态
    modelsLoading: false,
    modelsError: null,
    // 对比模式相关状态
    comparisonMode: false, // 是否开启对比模式
    comparisonStartTime: null, // 对比模式开启时间
    leftModel: "gpt-4", // 左侧（主要）模型
    rightModel: "claude-3", // 右侧（对比）模型
    comparisonMessages: [], // 右侧对比消息（临时存储，不保存到数据库）
    comparisonStreamingMessageId: null, // 右侧正在流式更新的消息ID
    comparisonIsStreamingActive: false, // 右侧是否有活跃的流式响应
  }),

  getters: {
    // 获取当前对话的消息
    currentMessages: (state) => {
      return state.messages.filter((msg) => msg.conversationId === state.currentConversationId)
    },

    // 检查是否有消息
    hasMessages: (state) => {
      return state.messages.length > 0
    },

    // 获取所有图片消息
    imageMessages: (state) => {
      return state.messages.filter((msg) => msg.type === "image")
    },

    // 获取当前对话的图片消息
    currentImageMessages: (state) => {
      return state.messages.filter(
        (msg) => msg.conversationId === state.currentConversationId && msg.type === "image"
      )
    },

    // 对比模式相关 getters
    // 获取左侧模型信息
    leftModelInfo: (state) => {
      return state.availableModels.find((model) => model.id === state.leftModel)
    },

    // 获取右侧模型信息
    rightModelInfo: (state) => {
      return state.availableModels.find((model) => model.id === state.rightModel)
    },

    // 获取当前对话的对比消息
    currentComparisonMessages: (state) => {
      return state.comparisonMessages.filter((msg) => msg.conversationId === state.currentConversationId)
    },
  },

  actions: {
    // 添加用户消息
    addUserMessage(content) {
      // 必须有有效的会话ID才能添加消息
      if (!this.currentConversationId) {
        console.error("❌ 添加用户消息失败：没有有效的会话ID")
        return null
      }

      const message = {
        id: Date.now(),
        type: "user",
        content: content.trim(),
        timestamp: new Date(),
        conversationId: this.currentConversationId,
      }

      this.messages.push(message)
      this.showWelcome = false

      return message
    },

    // 添加图片消息
    addImageMessage(imageData, caption = "") {
      // 必须有有效的会话ID才能添加消息
      if (!this.currentConversationId) {
        console.error("❌ 添加图片消息失败：没有有效的会话ID")
        return null
      }

      const message = {
        id: Date.now(),
        type: "image",
        content: caption,
        imageUrl: imageData.url,
        imageFile: imageData.file,
        imageName: imageData.name,
        timestamp: new Date(),
        conversationId: this.currentConversationId,
      }

      this.messages.push(message)
      this.showWelcome = false

      // 更新照片墙数据
      this.updatePhotoWall()

      return message
    },

    // 添加多图片消息
    addMultiImageMessage(imagesData, caption = "") {
      // 必须有有效的会话ID才能添加消息
      if (!this.currentConversationId) {
        console.error("❌ 添加多图片消息失败：没有有效的会话ID")
        return null
      }

      const message = {
        id: Date.now(),
        type: "multi-image",
        content: caption,
        images: imagesData.map((imageData) => ({
          url: imageData.url,
          file: imageData.file,
          name: imageData.name,
          id: imageData.id,
        })),
        timestamp: new Date(),
        conversationId: this.currentConversationId,
      }

      this.messages.push(message)
      this.showWelcome = false

      // 更新照片墙数据
      this.updatePhotoWall()

      return message
    },

    // 添加AI回复
    addAIMessage(content, modelId = null) {
      const message = {
        id: Date.now() + 1,
        type: "ai",
        content: content,
        timestamp: new Date(),
        conversationId: this.currentConversationId,
        isStreaming: false, // 标记是否为流式消息
        modelId: modelId || this.selectedModel, // 存储模型ID
      }

      this.messages.push(message)
      return message
    },

    // 更新AI消息内容（用于流式更新）
    updateAIMessage(messageId, content) {
      const messageIndex = this.messages.findIndex((msg) => msg.id === messageId)
      if (messageIndex !== -1) {
        this.messages[messageIndex].content = content
        this.messages[messageIndex].timestamp = new Date()
        this.messages[messageIndex].isStreaming = true
      }
    },

    // 删除消息
    removeMessage(messageId) {
      const messageIndex = this.messages.findIndex((msg) => msg.id === messageId)
      if (messageIndex !== -1) {
        this.messages.splice(messageIndex, 1)
        // 更新照片墙数据
        this.updatePhotoWall()
        return true
      }
      return false
    },

    // 完成流式消息更新
    completeStreamingMessage(messageId) {
      console.log("🔄 completeStreamingMessage 被调用:", {
        messageId,
        currentStreamingMessageId: this.streamingMessageId,
        isStreamingActive: this.isStreamingActive
      })

      const messageIndex = this.messages.findIndex((msg) => msg.id === messageId)
      if (messageIndex !== -1) {
        this.messages[messageIndex].isStreaming = false
        console.log("📝 消息流式状态已更新为 false")
      } else {
        console.warn("⚠️ 未找到要完成的消息:", messageId)
      }

      this.streamingMessageId = null
      this.isStreamingActive = false

      console.log("✅ 流式状态已清理:", {
        streamingMessageId: this.streamingMessageId,
        isStreamingActive: this.isStreamingActive
      })
    },

    // 开始流式消息
    startStreamingMessage(messageId) {
      console.log("▶️ startStreamingMessage 被调用:", {
        messageId,
        previousStreamingMessageId: this.streamingMessageId,
        previousIsStreamingActive: this.isStreamingActive
      })

      this.streamingMessageId = messageId
      this.isStreamingActive = true

      console.log("🎬 流式状态已设置:", {
        streamingMessageId: this.streamingMessageId,
        isStreamingActive: this.isStreamingActive
      })
    },

    // 创建新对话
    createNewConversation(sessionId, showWelcome = true) {
      // 必须传入API返回的sessionId，不再支持本地生成
      if (!sessionId) {
        console.error("❌ 创建新对话失败：必须提供API返回的会话ID")
        return null
      }

      this.currentConversationId = sessionId
      this.showWelcome = showWelcome
      this.isLoading = false

      // 触发会话创建事件，通知侧边栏刷新对话列表
      this.notifySessionCreated(sessionId)

      return sessionId
    },

    // 通知会话创建成功（用于刷新侧边栏对话列表）
    notifySessionCreated(sessionId) {
      // 使用自定义事件通知侧边栏刷新
      if (typeof window !== "undefined") {
        window.dispatchEvent(
          new CustomEvent("session-created", {
            detail: { sessionId },
          })
        )
      }
    },

    // 设置加载状态
    setLoading(loading) {
      this.isLoading = loading
    },

    // 全局加载弹窗相关方法
    setGlobalLoading(show, options = {}) {
      this.showGlobalLoading = show
      if (show) {
        this.globalLoadingText = options.text || "正在加载对话数据"
        this.globalLoadingSubText = options.subText || "请稍候..."
        this.globalLoadingProgress = options.progress || 0
        this.showGlobalProgress = options.showProgress || false
      }
    },

    updateGlobalLoadingProgress(progress, text = "") {
      this.globalLoadingProgress = progress
      if (text) {
        this.globalLoadingText = text
      }
    },

    // 切换欢迎页面显示
    setShowWelcome(show) {
      this.showWelcome = show
    },

    // 清空当前对话
    async clearCurrentConversation() {
      if (!this.currentConversationId) {
        this.showWelcome = true
        this.updatePhotoWall()
        return { success: true, message: "没有当前会话需要清空" }
      }

      try {
        // 调用后端API清空会话消息
        const result = await MessageService.deleteSessionMessages(this.currentConversationId)

        if (result.success) {
          // 后端删除成功后，清空本地消息
          this.messages = this.messages.filter(
            (msg) => msg.conversationId !== this.currentConversationId
          )
          this.showWelcome = true
          this.updatePhotoWall()

          return { success: true, message: "清空会话成功" }
        } else {
          console.error("清空会话失败:", result.error)
          return { success: false, message: result.message || "清空会话失败" }
        }
      } catch (error) {
        console.error("清空会话异常:", error)
        return { success: false, message: "清空会话失败，请稍后重试" }
      }
    },

    // 删除指定会话
    deleteConversation(conversationId) {
     
      // 移除该会话的所有消息
      this.messages = this.messages.filter((msg) => msg.conversationId !== conversationId)

      // 如果删除的是当前会话，清除当前会话状态并显示欢迎界面
      if (this.currentConversationId === conversationId) {
       
        this.currentConversationId = null
        this.showWelcome = true
        this.updatePhotoWall()
      }
    },

    // 切换照片墙显示状态
    togglePhotoWall() {
      this.showPhotoWall = !this.showPhotoWall
    },

    // 图片画布相关方法
    showImageCanvasPanel() {
      this.showImageCanvas = true
    },

    hideImageCanvas() {
      this.showImageCanvas = false
    },

    toggleImageCanvas() {
      this.showImageCanvas = !this.showImageCanvas
    },

    // 设置画布图片
    setCanvasImages(images) {
      this.canvasImages = images || []
    },

    // 添加图片到画布
    addImagesToCanvas(images) {
      if (!images || images.length === 0) return

      // 清空现有画布内容，显示新的图片
      this.canvasImages = images.map((image) => ({
        id: `canvas_${Date.now()}_${Math.random()}`,
        url: image.url,
        name: image.name,
        originalId: image.id,
        timestamp: new Date(),
      }))

      // 显示画布
      this.showImageCanvas = true

      // 触发自定义事件来收缩侧边栏
      window.dispatchEvent(new CustomEvent("collapse-sidebar"))
    },

    // 更新照片墙数据
    updatePhotoWall() {
      const imageMessages = []

      this.messages.forEach((msg) => {
        if (msg.type === "image") {
          // 单张图片消息
          imageMessages.push({
            id: msg.id,
            url: msg.imageUrl,
            name: msg.imageName,
            timestamp: msg.timestamp,
            conversationId: msg.conversationId,
            messageId: msg.id,
          })
        } else if (msg.type === "multi-image" && msg.images) {
          // 多张图片消息，为每张图片创建一个条目
          msg.images.forEach((image, index) => {
            imageMessages.push({
              id: `${msg.id}_${index}`,
              url: image.url,
              name: image.name,
              timestamp: msg.timestamp,
              conversationId: msg.conversationId,
              messageId: msg.id,
            })
          })
        } else if (msg.type === "user" && msg.imageUrls && msg.imageUrls.length > 0) {
          // 历史记录中的用户图片消息（从API加载的消息）
          msg.imageUrls.forEach((image, index) => {
            imageMessages.push({
              id: `${msg.id}_history_${index}`,
              url: image.url,
              name: image.path || `图片_${index + 1}`,
              timestamp: msg.timestamp,
              conversationId: msg.conversationId,
              messageId: msg.id,
            })
          })
        }
      })

      console.log('📸 照片墙更新:', {
        totalMessages: this.messages.length,
        imageMessages: imageMessages.length,
        imageMessagesData: imageMessages
      })

      this.photoWallImages = imageMessages.sort(
        (a, b) => new Date(b.timestamp) - new Date(a.timestamp)
      )
    },

    // 定位到指定消息
    scrollToMessage(messageId) {
      // 这个方法会被组件调用来滚动到指定消息
      const message = this.messages.find((msg) => msg.id === messageId)
      if (message) {
        // 如果消息不在当前对话中，切换到对应对话
        if (message.conversationId !== this.currentConversationId) {
          this.currentConversationId = message.conversationId
          this.showWelcome = false
        }
        return message
      }
      return null
    },

    // 加载会话消息列表
    async loadConversationMessages(sessionId) {
      if (!sessionId) {
        console.error("❌ 加载会话消息失败：sessionId 不能为空")
        return { success: false, message: "sessionId 不能为空" }
      }

      // 显示全局加载弹窗
      this.setGlobalLoading(true, {
        text: "正在加载对话数据",
        subText: "请稍候...",
        showProgress: true,
        progress: 0
      })

      // 设置加载状态
      this.isLoading = true

      try {
        // 步骤1: 获取消息列表
        this.updateGlobalLoadingProgress(20, "正在获取消息列表...")
        const result = await MessageService.getMessageList(sessionId)

        if (result.success) {
          // 步骤2: 格式化消息数据
          this.updateGlobalLoadingProgress(50, "正在处理消息数据...")
          const formattedMessages = MessageService.formatMessagesForRender(result.data, sessionId)

          // 步骤3: 预加载图片
          this.updateGlobalLoadingProgress(70, "正在加载图片资源...")
          const messagesWithImages = await MessageService.preloadMessageImages(formattedMessages)

          // 步骤4: 更新界面数据
          this.updateGlobalLoadingProgress(90, "正在更新界面...")

          // 清空当前消息并设置新消息
          this.messages = messagesWithImages

          // 设置当前会话ID
          this.currentConversationId = sessionId

          // 隐藏欢迎页面
          this.showWelcome = false

          // 更新照片墙数据
          this.updatePhotoWall()

          // 完成加载
          this.updateGlobalLoadingProgress(100, "加载完成")

          // 延迟一点时间让用户看到完成状态
          await new Promise(resolve => setTimeout(resolve, 300))

          return {
            success: true,
            data: messagesWithImages,
            message: "会话消息加载成功",
          }
        } else {
          console.error("❌ 会话消息加载失败:", result.error)
          return {
            success: false,
            message: result.message || "会话消息加载失败",
          }
        }
      } catch (error) {
        console.error("❌ 会话消息加载异常:", error)
        return {
          success: false,
          message: "网络错误，请稍后重试",
        }
      } finally {
        // 清除加载状态
        this.isLoading = false
        // 隐藏全局加载弹窗
        this.setGlobalLoading(false)
      }
    },

    // 查找AI消息对应的用户询问
    findUserMessageForAI(aiMessageId) {
      // 找到AI消息
      const aiMessage = this.messages.find((msg) => msg.id === aiMessageId && msg.type === "ai")
      if (!aiMessage) return null

      // 在同一对话中查找AI消息之前的最近一条用户消息（包括图片消息）
      const conversationMessages = this.messages
        .filter((msg) => msg.conversationId === aiMessage.conversationId)
        .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))

      const aiMessageIndex = conversationMessages.findIndex((msg) => msg.id === aiMessageId)
      if (aiMessageIndex === -1) return null

      // 从AI消息位置向前查找最近的用户消息或图片消息
      for (let i = aiMessageIndex - 1; i >= 0; i--) {
        const msg = conversationMessages[i]
        if (msg.type === "user" || msg.type === "image" || msg.type === "multi-image") {
          return msg
        }
      }

      return null
    },

    // 将图片添加到照片墙
    addImagesToPhotoWall(images) {
      if (!images || images.length === 0) return

      // 为每张图片创建照片墙条目
      const newPhotoWallImages = images.map((image) => ({
        id: `photowall_${Date.now()}_${Math.random()}`,
        url: image.url,
        name: image.name,
        timestamp: new Date(),
        conversationId: this.currentConversationId,
        messageId: image.id, // 原始消息ID
        isFromUserQuery: true, // 标记这是来自用户询问的图片
      }))

      // 添加到照片墙，避免重复
      newPhotoWallImages.forEach((newImage) => {
        const exists = this.photoWallImages.some(
          (existing) => existing.url === newImage.url && existing.messageId === newImage.messageId
        )
        if (!exists) {
          this.photoWallImages.unshift(newImage) // 添加到开头，最新的在前面
        }
      })

      // 重新排序照片墙图片
      this.photoWallImages.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
    },

    // API相关方法
    setApiError(error) {
      this.apiError = error
    },

    clearApiError() {
      this.apiError = null
    },

    setConnectionStatus(status) {
      this.connectionStatus = status
    },

    // 获取对话上下文（用于API调用）
    getConversationContext(limit = 10) {
      return this.currentMessages.slice(-limit).map((msg) => ({
        role: msg.type === "user" ? "user" : "assistant",
        content: msg.content,
        timestamp: msg.timestamp,
      }))
    },

    // 模型选择相关方法
    setSelectedModel(modelId) {
      this.selectedModel = modelId
      // 保存到本地存储
      localStorage.setItem("dolphin-selected-model", modelId)
    },

    getSelectedModel() {
      return this.availableModels.find((model) => model.id === this.selectedModel)
    },

    // 从API获取模型列表
    async fetchModels() {
      this.modelsLoading = true
      this.modelsError = null

      try {
        const result = await ModelService.getModelList()

        if (result.success && result.data) {
          // 格式化模型数据
          const formattedModels = ModelService.formatModels(result.data)

          if (formattedModels.length > 0) {
            this.availableModels = formattedModels

            // 如果当前选中的模型不在新列表中，选择第一个模型
            const currentModel = this.getSelectedModel()
            if (!currentModel) {
              this.setSelectedModel(formattedModels[0].id)
            }
          } else {
            console.warn("🤖 格式化后的模型列表为空，保持默认模型")
          }
        } else {
          throw new Error(result.message || "获取模型列表失败")
        }
      } catch (error) {
        console.error("🤖 获取模型列表失败:", error)
        this.modelsError = error.message
        // 保持默认模型列表，不清空
      } finally {
        this.modelsLoading = false
      }
    },

    // 初始化模型相关数据
    initModels() {
      // 从本地存储恢复选中的模型
      const savedModel = localStorage.getItem("dolphin-selected-model")
      if (savedModel) {
        this.selectedModel = savedModel
      }

      // 初始化对比模式设置
      this.initComparisonMode()

      // 获取最新的模型列表
      this.fetchModels()
    },

    // 清除模型错误
    clearModelsError() {
      this.modelsError = null
    },

    // 消息编辑相关方法
    startEditingMessage(messageId) {
      const message = this.messages.find((msg) => msg.id === messageId)
      if (message && message.type === "user") {
        this.editingMessageId = messageId
        this.editingContent = message.content
        return true
      }
      return false
    },

    cancelEditingMessage() {
      this.editingMessageId = null
      this.editingContent = ""
    },

    updateEditingContent(content) {
      this.editingContent = content
    },

    // 完成消息编辑
    async finishEditingMessage(newContent) {
      if (!this.editingMessageId) return { success: false, message: "没有正在编辑的消息" }

      const messageIndex = this.messages.findIndex((msg) => msg.id === this.editingMessageId)
      if (messageIndex === -1) {
        this.cancelEditingMessage()
        return { success: false, message: "找不到要编辑的消息" }
      }

      const originalMessage = this.messages[messageIndex]

      // 更新消息内容
      this.messages[messageIndex].content = newContent.trim()
      this.messages[messageIndex].timestamp = new Date()

      // 查找并删除该用户消息之后的所有AI回复
      const messagesToRemove = []
      for (let i = messageIndex + 1; i < this.messages.length; i++) {
        const msg = this.messages[i]
        if (msg.type === "ai" && msg.conversationId === originalMessage.conversationId) {
          messagesToRemove.push(msg.id)
          break // 只删除紧接着的第一个AI回复
        }
      }

      // 删除AI回复消息
      messagesToRemove.forEach((id) => this.removeMessage(id))

      // 清除编辑状态
      this.cancelEditingMessage()

      return {
        success: true,
        message: "消息编辑成功",
        updatedMessage: this.messages[messageIndex],
      }
    },

    // 重新生成AI消息
    async regenerateMessage(aiMessageId) {
      try {
        // 找到要重新生成的AI消息
        const aiMessage = this.messages.find((msg) => msg.id === aiMessageId && msg.type === "ai")
        if (!aiMessage) {
          console.error("❌ 重新生成失败：找不到AI消息")
          return { success: false, message: "找不到要重新生成的消息" }
        }

        // 找到对应的用户消息
        const userMessage = this.findUserMessageForAI(aiMessageId)
        if (!userMessage) {
          console.error("❌ 重新生成失败：找不到对应的用户消息")
          return { success: false, message: "找不到对应的用户消息" }
        }

        // 删除原来的AI消息
        this.removeMessage(aiMessageId)

        // 设置加载状态
        this.setLoading(true)

        // 准备重新发送的数据
        const requestData = {
          question: userMessage.content,
          images: [],
          deepThinking: false,
          modelId: this.selectedModel,
          sessionId: this.currentConversationId,
        }

        // 如果用户消息包含图片，添加图片信息
        if (userMessage.type === "image" && userMessage.imageUrl) {
          // 这里需要根据实际API要求处理图片数据
          // 可能需要图片ID或其他格式
        } else if (userMessage.type === "multi-image" && userMessage.images) {
          // 处理多图片情况
        }

        // 创建新的AI消息用于流式更新
        const newAIMessage = this.addAIMessage("", this.selectedModel)
        this.startStreamingMessage(newAIMessage.id)
        let accumulatedContent = ""

        // 调用流式API重新生成回复
        const { ChatService } = await import("@/api/chat")

        const result = await ChatService.getAIReplyStream(
          requestData,
          (chunk) => {
            // 累积流式数据，与现有发送消息逻辑保持一致
            if (chunk) {
              accumulatedContent += chunk
              this.updateAIMessage(newAIMessage.id, accumulatedContent)
            }
          },
          (error) => {
            console.error("❌ 重新生成流式回复错误:", error)
            this.updateAIMessage(newAIMessage.id, "❌ 重新生成失败，请稍后再试。")
            this.completeStreamingMessage(newAIMessage.id)
            this.setLoading(false)
          },
          () => {
            // 完成回调
            this.completeStreamingMessage(newAIMessage.id)
            this.setLoading(false)
          }
        )

        if (result.success) {
          return { success: true, message: "开始重新生成消息" }
        } else {
          this.removeMessage(newAIMessage.id)
          this.setLoading(false)
          return { success: false, message: result.message || "重新生成失败" }
        }
      } catch (error) {
        console.error("❌ 重新生成消息异常:", error)
        this.setLoading(false)
        return { success: false, message: "重新生成失败，请稍后重试" }
      }
    },

    // 对比模式相关方法
    // 切换对比模式
    toggleComparisonMode() {
      this.comparisonMode = !this.comparisonMode
      if (this.comparisonMode) {
        // 开启对比模式时记录开启时间
        this.comparisonStartTime = new Date()
      } else {
        // 关闭对比模式时清空对比消息和开启时间
        this.comparisonMessages = []
        this.comparisonStreamingMessageId = null
        this.comparisonIsStreamingActive = false
        this.comparisonStartTime = null
      }
    },

    // 设置左侧模型
    setLeftModel(modelId) {
      this.leftModel = modelId
      // 只在非对比模式下更新主要的选中模型
      if (!this.comparisonMode) {
        this.selectedModel = modelId
      }
      localStorage.setItem("dolphin-left-model", modelId)
    },

    // 设置右侧模型
    setRightModel(modelId) {
      this.rightModel = modelId
      localStorage.setItem("dolphin-right-model", modelId)
    },

    // 添加对比消息（右侧）
    addComparisonMessage(content, type = "ai", modelId = null) {
      const message = {
        id: Date.now() + Math.random(), // 确保ID唯一
        type: type,
        content: content,
        timestamp: new Date(),
        conversationId: this.currentConversationId,
        isStreaming: false,
        modelId: modelId || this.rightModel, // 存储模型ID，默认使用右侧模型
      }

      this.comparisonMessages.push(message)
      return message
    },

    // 更新对比消息内容（用于流式更新）
    updateComparisonMessage(messageId, content) {
      const messageIndex = this.comparisonMessages.findIndex((msg) => msg.id === messageId)
      if (messageIndex !== -1) {
        this.comparisonMessages[messageIndex].content = content
        this.comparisonMessages[messageIndex].timestamp = new Date()
        this.comparisonMessages[messageIndex].isStreaming = true
      }
    },

    // 开始对比消息流式更新
    startComparisonStreamingMessage(messageId) {
      this.comparisonStreamingMessageId = messageId
      this.comparisonIsStreamingActive = true
    },

    // 完成对比消息流式更新
    completeComparisonStreamingMessage(messageId) {
      if (this.comparisonStreamingMessageId === messageId) {
        this.comparisonStreamingMessageId = null
        this.comparisonIsStreamingActive = false

        // 标记消息为完成状态
        const messageIndex = this.comparisonMessages.findIndex((msg) => msg.id === messageId)
        if (messageIndex !== -1) {
          this.comparisonMessages[messageIndex].isStreaming = false
        }
      }
    },

    // 清空对比消息
    clearComparisonMessages() {
      this.comparisonMessages = []
      this.comparisonStreamingMessageId = null
      this.comparisonIsStreamingActive = false
    },

    // 初始化对比模式设置
    initComparisonMode() {
      // 从本地存储恢复模型选择
      const savedLeftModel = localStorage.getItem("dolphin-left-model")
      const savedRightModel = localStorage.getItem("dolphin-right-model")

      if (savedLeftModel) {
        this.leftModel = savedLeftModel
      }
      if (savedRightModel) {
        this.rightModel = savedRightModel
      }

      // 确保左右模型不同
      if (this.leftModel === this.rightModel) {
        // 如果相同，为右侧选择一个不同的默认模型
        const availableIds = ["gpt-4", "claude-3", "qwen3-4b", "gemini-pro"]
        const differentModel = availableIds.find(id => id !== this.leftModel)
        if (differentModel) {
          this.rightModel = differentModel
          localStorage.setItem("dolphin-right-model", differentModel)
        }
      }
    },
  },
})
