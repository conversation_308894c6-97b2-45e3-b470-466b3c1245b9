<script setup>
import { ref, computed } from 'vue'
import { useAuthStore } from '@/stores/authstore'
import { useRouter } from 'vue-router'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  message: {
    type: String,
    default: '您的密码已过期，请重新登录。'
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 状态管理
const authStore = useAuthStore()
const router = useRouter()

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 方法
const handleConfirm = () => {
  // 关闭弹窗
  dialogVisible.value = false
  
  // 登出并跳转到登录页
  authStore.logout()
  router.push('/auth/login')
}

// 阻止用户关闭弹窗（强制处理）
const preventClose = () => {
  // 不允许用户直接关闭，必须点击确认按钮
  return false
}
</script>

<template>
  <VDialog 
    v-model="dialogVisible" 
    max-width="400" 
    persistent
    no-click-animation
    class="password-expired-dialog"
    @click:outside="preventClose"
    @keydown.esc="preventClose"
  >
    <VCard class="dialog-card">
      <VCardTitle class="dialog-title">
        <VIcon icon="mdi-alert-circle" color="warning" class="me-2" />
        密码过期提醒
      </VCardTitle>

      <VCardText class="dialog-content">
        <div class="message-container">
          <VIcon 
            icon="mdi-lock-alert" 
            color="warning" 
            size="48" 
            class="warning-icon"
          />
          <p class="message-text">{{ message }}</p>
          <p class="sub-message">为了您的账户安全，请重新登录。</p>
        </div>
      </VCardText>

      <VCardActions class="dialog-actions">
        <VSpacer />
        <VBtn
          variant="flat"
          color="primary"
          size="large"
          @click="handleConfirm"
          class="confirm-btn"
        >
          确定
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>

<style scoped>
.password-expired-dialog :deep(.v-overlay__content) {
  margin: 24px;
}

.dialog-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  background: var(--v-theme-surface);
}

.dialog-title {
  padding: 20px 24px 16px;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--v-theme-on-surface);
  border-bottom: 1px solid var(--v-theme-surface-variant);
  display: flex;
  align-items: center;
}

.dialog-content {
  padding: 24px;
}

.message-container {
  text-align: center;
  padding: 16px 0;
}

.warning-icon {
  margin-bottom: 16px;
  opacity: 0.8;
}

.message-text {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--v-theme-on-surface);
  margin-bottom: 8px;
  line-height: 1.5;
}

.sub-message {
  font-size: 0.9rem;
  color: var(--v-theme-on-surface-variant);
  margin-bottom: 0;
  line-height: 1.4;
}

.dialog-actions {
  padding: 16px 24px 20px;
  border-top: 1px solid var(--v-theme-surface-variant);
}

.confirm-btn {
  min-width: 100px;
  font-weight: 600;
}

/* 暗色主题适配 */
.v-theme--dark .dialog-card {
  background-color: var(--v-theme-surface);
}

.v-theme--dark .dialog-title {
  border-bottom-color: rgba(255, 255, 255, 0.12);
}

.v-theme--dark .dialog-actions {
  border-top-color: rgba(255, 255, 255, 0.12);
}

/* 动画效果 */
.dialog-card {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .password-expired-dialog :deep(.v-overlay__content) {
    margin: 16px;
  }
  
  .dialog-title {
    padding: 16px 20px 12px;
    font-size: 1.1rem;
  }
  
  .dialog-content {
    padding: 20px;
  }
  
  .message-text {
    font-size: 1rem;
  }
  
  .dialog-actions {
    padding: 12px 20px 16px;
  }
}
</style>
