import { ref, nextTick, computed } from "vue"
import { useChatStore } from "@/stores/baseStore"
import { ChatService } from "@/api/chat"
import { SessionService } from "@/api/session"

/**
 * 提取会话ID的辅助函数
 * @param {*} responseData - API响应数据
 * @returns {string|null} - 提取的会话ID
 */
function extractSessionId(responseData) {
  if (!responseData) return null

  // 处理不同的响应格式
  if (responseData.id) {
    return responseData.id
  } else if (responseData.data && responseData.data.id) {
    return responseData.data.id
  } else if (typeof responseData === 'string') {
    return responseData
  }

  return null
}

/**
 * 生成会话标题的辅助函数
 * @param {string} userMessage - 用户消息
 * @returns {string} - 生成的会话标题
 */
function generateSessionTitle(userMessage) {
  if (!userMessage || typeof userMessage !== 'string') {
    return '新对话'
  }

  // 清理消息内容
  const cleanMessage = userMessage.trim()
    .replace(/\s+/g, ' ') // 将多个空格替换为单个空格
    .replace(/[^\u4e00-\u9fa5\w\s.,!?，。！？]/g, '') // 保留中文、英文、数字、基本标点

  // 如果清理后为空，返回默认标题
  if (!cleanMessage) {
    return '新对话'
  }

  // 截取前30个字符作为标题
  const maxLength = 30
  if (cleanMessage.length <= maxLength) {
    return cleanMessage
  }

  // 如果超过长度，在合适的位置截断
  const truncated = cleanMessage.substring(0, maxLength)

  // 尝试在最后一个完整词或标点处截断
  const lastSpace = truncated.lastIndexOf(' ')
  const lastPunctuation = Math.max(
    truncated.lastIndexOf('，'),
    truncated.lastIndexOf('。'),
    truncated.lastIndexOf('！'),
    truncated.lastIndexOf('？'),
    truncated.lastIndexOf(','),
    truncated.lastIndexOf('.'),
    truncated.lastIndexOf('!'),
    truncated.lastIndexOf('?')
  )

  const cutPoint = Math.max(lastSpace, lastPunctuation)

  if (cutPoint > 15) { // 确保截断点不会太靠前
    return truncated.substring(0, cutPoint)
  }

  return truncated + '...'
}

/**
 * 检查会话是否需要重命名
 * @param {string} sessionId - 会话ID
 * @param {Set} newlyCreatedSessions - 新创建的会话集合
 * @returns {Promise<boolean>} - 是否需要重命名
 */
async function shouldRenameSession(sessionId, newlyCreatedSessions) {
  // 如果是新创建的会话，直接返回true
  if (newlyCreatedSessions.has(sessionId)) {
    return true
  }

  // 检查会话列表中是否存在名为"新对话"的会话
  try {
    const result = await SessionService.getSessionList()
    if (result.success && result.data) {
      const session = result.data.find(s => s.id === sessionId)
      return session && (session.name === '新对话' || session.name === 'undefined')
    }
  } catch (error) {
    console.warn('检查会话名称时出错:', error)
  }

  return false
}

/**
 * 消息发送相关逻辑
 */
export function useMessageSend() {
  const chatStore = useChatStore()

  // 响应式数据
  const deepThinkActive = ref(false)
  const webSearchActive = ref(false)

  // 流式响应控制器
  const streamController = ref(null)

  // 跟踪新创建的会话，用于判断是否需要自动重命名
  const newlyCreatedSessions = ref(new Set())

  // 计算属性：是否正在流式响应
  const isStreaming = computed(() => chatStore.isStreamingActive)

  /**
   * 发送消息
   * @param {string} message - 消息内容
   * @param {Array} attachedImages - 附件图片
   * @param {Function} clearAttachedImages - 清空附件的回调
   * @param {Function} resetTextareaHeight - 重置输入框高度的回调
   * @param {Function} scrollToBottomImmediate - 滚动到底部的回调
   */
  const handleSend = async (
    message,
    attachedImages,
    clearAttachedImages,
    resetTextareaHeight,
    scrollToBottomImmediate
  ) => {
    if ((!message.trim() && attachedImages.length === 0) || chatStore.isLoading) return

    const userMessage = message.trim()

    // 检查是否有图片正在上传
    const hasUploadingImages = attachedImages.some((img) => img.uploading)
    if (hasUploadingImages) {
      console.warn("⚠️ 有图片正在上传中，请等待上传完成后再发送")
      return
    }

    // 检查是否有上传失败的图片
    const hasFailedImages = attachedImages.some((img) => img.uploadError)
    if (hasFailedImages) {
      console.warn("⚠️ 有图片上传失败，请重新上传后再发送")
      return
    }

    // 收集已上传成功的图片路径
    const uploadedImages = attachedImages
      .filter((img) => img.uploaded && img.serverData)
      .map((img) => {
        // 新接口直接返回图片路径，如: "chat/image/1944575038917316608.png"
        return img.serverData
      })
      .filter((path) => path) // 过滤掉空值

    // 设置加载状态
    chatStore.setLoading(true)

    try {
      // 检查是否有有效的会话ID，如果没有则自动创建
      let isNewSession = false
      if (!chatStore.currentConversationId) {
        console.log('🔄 没有活跃会话，正在自动创建新会话...')

        try {
          // 自动创建新会话
          const result = await SessionService.createSession({
            name: '新对话',
            description: ''
          })

          if (result.success && result.data) {
            // 提取会话ID并设置到store
            const sessionId = extractSessionId(result.data)
            if (sessionId) {
              // 自动创建会话时不显示欢迎页面，直接进入对话界面
              chatStore.createNewConversation(sessionId, false)

              // 标记这是新创建的会话，需要在发送消息后重命名
              newlyCreatedSessions.value.add(sessionId)
              isNewSession = true

              console.log('✅ 自动创建会话成功，ID:', sessionId)
            } else {
              throw new Error('API返回的数据格式不正确，未找到会话ID')
            }
          } else {
            throw new Error(result.error || result.message || '创建会话失败')
          }
        } catch (error) {
          console.error('❌ 自动创建会话失败:', error)
          chatStore.addAIMessage('❌ 创建会话失败，请稍后重试')
          return
        }
      }

      // 现在有了会话ID，添加用户消息到界面显示
      if (attachedImages.length > 0) {
        if (attachedImages.length === 1) {
          // 单张图片使用原有逻辑
          chatStore.addImageMessage(attachedImages[0], userMessage)
        } else {
          // 多张图片使用新的多图片消息类型
          chatStore.addMultiImageMessage(attachedImages, userMessage)
        }
        // 清空附件
        clearAttachedImages()
      } else if (userMessage) {
        // 发送文本消息
        chatStore.addUserMessage(userMessage)
      }

      // 确保不再显示欢迎页面
      chatStore.setShowWelcome(false)

      // 立即滚动到底部（用户消息）
      nextTick(() => {
        scrollToBottomImmediate()
      })

      // 重置textarea高度到初始状态
      resetTextareaHeight()

      // 检查是否为对比模式
      if (chatStore.comparisonMode) {
        // 对比模式：同时调用两个模型
        await handleComparisonModeSend(
          userMessage,
          uploadedImages,
          scrollToBottomImmediate,
          isNewSession
        )
      } else {
        // 普通模式：调用单个模型
        await handleNormalModeSend(
          userMessage,
          uploadedImages,
          scrollToBottomImmediate,
          isNewSession
        )
      }


      // 处理会话重命名（如果是新会话）
      if (isNewSession && userMessage && chatStore.currentConversationId) {
        await handleSessionRename(userMessage, chatStore.currentConversationId)
      }
    } catch (error) {
      console.error("❌ 发送消息失败:", error)

      // 根据错误类型显示不同的错误信息
      let errorMessage = "抱歉，发生了错误，请稍后再试。"

      if (error.code === "NETWORK_ERROR" || error.message.includes("Network Error")) {
        errorMessage = "网络连接失败，请检查网络设置后重试。"
      } else if (error.response?.status === 401) {
        errorMessage = "身份验证失败，请重新登录。"
      } else if (error.response?.status === 429) {
        errorMessage = "请求过于频繁，请稍后再试。"
      } else if (error.response?.status >= 500) {
        errorMessage = "服务器暂时不可用，请稍后再试。"
      }

      chatStore.addAIMessage(`❌ ${errorMessage}`)
    } finally {
      chatStore.setLoading(false)
    }
  }

  /**
   * 处理深度思考
   */
  const handleDeepThink = () => {

    deepThinkActive.value = !deepThinkActive.value
    // 这里可以添加深度思考逻辑
  }

  /**
   * 处理联网搜索
   */
  const handleWebSearch = () => {

    webSearchActive.value = !webSearchActive.value
    // 这里可以添加联网搜索逻辑
  }

  /**
   * 停止流式响应
   */
  const stopStreaming = () => {
    try {
      if (streamController.value) {
        // 中断流式请求
        streamController.value.abort()
        console.log("🛑 已停止流式响应")

        // 清理状态
        if (chatStore.streamingMessageId) {
          chatStore.completeStreamingMessage(chatStore.streamingMessageId)
        }
        streamController.value = null
        chatStore.setLoading(false)
      }
    } catch (error) {
      console.error("❌ 停止流式响应失败:", error)
    }
  }

  /**
   * 处理键盘按下事件
   * @param {KeyboardEvent} event - 键盘事件
   * @param {Function} sendCallback - 发送消息的回调
   */
  const handleKeydown = (event, sendCallback) => {
    // Enter 发送消息 (不需要Ctrl)
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault()
      sendCallback()
    }
  }

  // 处理对比模式的消息发送
  const handleComparisonModeSend = async (userMessage, uploadedImages, scrollToBottomImmediate, isNewSession) => {
    // 创建左侧（主要）AI消息，传递左侧模型ID
    const leftAIMessage = chatStore.addAIMessage("", chatStore.leftModel)
    chatStore.startStreamingMessage(leftAIMessage.id)
    let leftAccumulatedContent = ""

    // 创建右侧（对比）AI消息，传递右侧模型ID
    const rightAIMessage = chatStore.addComparisonMessage("", "ai", chatStore.rightModel)
    chatStore.startComparisonStreamingMessage(rightAIMessage.id)
    let rightAccumulatedContent = ""

    // 准备左侧请求数据
    const leftRequestData = {
      question: userMessage,
      images: uploadedImages,
      deepThinking: deepThinkActive.value,
      modelId: chatStore.leftModel,
      sessionId: chatStore.currentConversationId,
    }

    // 准备右侧请求数据
    const rightRequestData = {
      question: userMessage,
      images: uploadedImages,
      deepThinking: deepThinkActive.value,
      modelId: chatStore.rightModel,
      sessionId: chatStore.currentConversationId,
    }

    // 同时调用两个模型的API
    const [leftResult, rightResult] = await Promise.allSettled([
      // 左侧模型调用
      ChatService.getAIReplyStream(
        leftRequestData,
        (data) => {
          if (data) {
            leftAccumulatedContent += data
            chatStore.updateAIMessage(leftAIMessage.id, leftAccumulatedContent)
            nextTick(() => scrollToBottomImmediate())
          }
        },
        (error) => {
          console.error("❌ 左侧模型回复失败:", error)
          const errorMessage = `❌ ${error.message || "左侧模型服务暂时不可用"}`
          chatStore.updateAIMessage(leftAIMessage.id, leftAccumulatedContent + "\n\n" + errorMessage)
          chatStore.completeStreamingMessage(leftAIMessage.id)
        },
        () => {
          chatStore.completeStreamingMessage(leftAIMessage.id)
          nextTick(() => scrollToBottomImmediate())
        }
      ),
      // 右侧模型调用
      ChatService.getAIReplyStream(
        rightRequestData,
        (data) => {
          if (data) {
            rightAccumulatedContent += data
            chatStore.updateComparisonMessage(rightAIMessage.id, rightAccumulatedContent)
            nextTick(() => scrollToBottomImmediate())
          }
        },
        (error) => {
          console.error("❌ 右侧模型回复失败:", error)
          const errorMessage = `❌ ${error.message || "右侧模型服务暂时不可用"}`
          chatStore.updateComparisonMessage(rightAIMessage.id, rightAccumulatedContent + "\n\n" + errorMessage)
          chatStore.completeComparisonStreamingMessage(rightAIMessage.id)
        },
        () => {
          chatStore.completeComparisonStreamingMessage(rightAIMessage.id)
          nextTick(() => scrollToBottomImmediate())
        }
      )
    ])

    // 处理结果
    if (leftResult.status === 'fulfilled' && leftResult.value.success && leftResult.value.controller) {
      streamController.value = leftResult.value.controller
    }

    // 检查是否有失败的请求
    if (leftResult.status === 'rejected') {
      console.error("❌ 左侧模型请求失败:", leftResult.reason)
      chatStore.updateAIMessage(leftAIMessage.id, "❌ 左侧模型请求失败")
      chatStore.completeStreamingMessage(leftAIMessage.id)
    }

    if (rightResult.status === 'rejected') {
      console.error("❌ 右侧模型请求失败:", rightResult.reason)
      chatStore.updateComparisonMessage(rightAIMessage.id, "❌ 右侧模型请求失败")
      chatStore.completeComparisonStreamingMessage(rightAIMessage.id)
    }

    chatStore.setLoading(false)
  }

  // 处理普通模式的消息发送
  const handleNormalModeSend = async (userMessage, uploadedImages, scrollToBottomImmediate, isNewSession) => {
    const requestData = {
      question: userMessage,
      images: uploadedImages,
      deepThinking: deepThinkActive.value,
      modelId: chatStore.selectedModel,
      sessionId: chatStore.currentConversationId,
    }

    // 创建AI消息占位符，传递当前选中的模型ID
    const aiMessage = chatStore.addAIMessage("", chatStore.selectedModel)
    console.log("🚀 开始流式响应:", {
      aiMessageId: aiMessage.id,
      selectedModel: chatStore.selectedModel,
      sessionId: chatStore.currentConversationId
    })
    chatStore.startStreamingMessage(aiMessage.id)
    let accumulatedContent = ""

    // 调用流式API
    const result = await ChatService.getAIReplyStream(
      requestData,
      // onMessage - 处理流式数据
      (data) => {
        if (data) {
          accumulatedContent += data
          chatStore.updateAIMessage(aiMessage.id, accumulatedContent)
          nextTick(() => {
            scrollToBottomImmediate()
          })
        }
      },
      // onError - 处理错误
      (error) => {
        console.error("❌ 流式AI回复失败:", error)
        console.log("🔧 错误回调中清理状态:", {
          errorName: error.name,
          errorMessage: error.message,
          streamingMessageId: chatStore.streamingMessageId,
          isStreamingActive: chatStore.isStreamingActive
        })
        const errorMessage = `❌ ${error.message || "AI服务暂时不可用，请稍后再试。"}`
        chatStore.updateAIMessage(aiMessage.id, accumulatedContent + "\n\n" + errorMessage)
        chatStore.completeStreamingMessage(aiMessage.id)
        streamController.value = null
        chatStore.setLoading(false)
      },
      // onComplete - 完成回调
      () => {
        console.log("🎉 收到完成回调，开始清理状态")
        console.log("📋 当前状态:", {
          streamingMessageId: chatStore.streamingMessageId,
          isStreamingActive: chatStore.isStreamingActive,
          hasController: !!streamController.value,
          aiMessageId: aiMessage.id
        })

        chatStore.completeStreamingMessage(aiMessage.id)
        streamController.value = null
        chatStore.setLoading(false)

        console.log("✨ 状态清理完成:", {
          streamingMessageId: chatStore.streamingMessageId,
          isStreamingActive: chatStore.isStreamingActive,
          hasController: !!streamController.value
        })

        nextTick(() => {
          scrollToBottomImmediate()
        })
      }
    )

    if (result.success && result.controller) {
      streamController.value = result.controller
    } else {
      const errorMessage = result.error || result.message || "抱歉，AI服务暂时不可用，请稍后再试。"
      chatStore.updateAIMessage(aiMessage.id, `❌ ${errorMessage}`)
      chatStore.completeStreamingMessage(aiMessage.id)
      chatStore.setLoading(false)
    }
  }

  // 处理会话重命名
  const handleSessionRename = async (userMessage, sessionId) => {
    try {
      const needsRename = await shouldRenameSession(sessionId, newlyCreatedSessions.value)

      if (needsRename) {
        const sessionTitle = generateSessionTitle(userMessage)
        console.log('🔄 正在自动重命名会话:', sessionTitle)

        const renameResult = await SessionService.renameSession(sessionId, sessionTitle)

        if (renameResult.success) {
          console.log('✅ 会话自动重命名成功:', sessionTitle)
          newlyCreatedSessions.value.delete(sessionId)

          // 触发会话列表刷新
          if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('session-renamed', {
              detail: {
                sessionId: sessionId,
                newTitle: sessionTitle
              }
            }))
          }
        } else {
          console.warn('⚠️ 会话自动重命名失败:', renameResult.message)
        }
      }
    } catch (error) {
      console.error('❌ 会话自动重命名异常:', error)
    }
  }

  return {
    // 响应式数据
    deepThinkActive,
    webSearchActive,
    isStreaming,

    // 方法
    handleSend,
    handleDeepThink,
    handleWebSearch,
    handleKeydown,
    stopStreaming,
  }
}
