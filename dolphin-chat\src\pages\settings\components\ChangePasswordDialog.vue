<script setup>
import { ref, computed } from 'vue'
import { UserService } from '@/api/user'
import { useAuthStore } from '@/stores/authstore'
import { useRouter } from 'vue-router'
import PasswordExpiredDialog from '@/components/common/PasswordExpiredDialog.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 状态管理
const authStore = useAuthStore()
const router = useRouter()

// 响应式数据
const password = ref('')
const loading = ref(false)
const error = ref('')
const showPassword = ref(false)
const showExpiredDialog = ref(false)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isPasswordValid = computed(() => {
  return password.value.trim().length >= 4 // 最少4位密码
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  password.value = ''
  error.value = ''
  showPassword.value = false
  showExpiredDialog.value = false
}

const handleSubmit = async () => {
  if (!isPasswordValid.value) {
    error.value = '密码长度至少4位'
    return
  }

  loading.value = true
  error.value = ''

  try {
    const result = await UserService.changePassword(password.value)

    if (result.success) {
      // 关闭弹窗
      dialogVisible.value = false
      resetForm()

      // 显示密码过期提示弹窗
      setTimeout(() => {
        showExpiredDialog.value = true
      }, 300)
    } else {
      error.value = result.message || '密码修改失败'
    }
  } catch (err) {
    error.value = '网络错误，请稍后重试'
  } finally {
    loading.value = false
  }
}

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}
</script>

<template>
  <VDialog
    v-model="dialogVisible"
    max-width="400"
    persistent
    class="change-password-dialog"
  >
    <VCard class="dialog-card">
      <VCardTitle class="dialog-title">
        <VIcon icon="mdi-lock-reset" color="primary" class="me-2" />
        修改密码
      </VCardTitle>

      <VCardText class="dialog-content">
        <!-- 错误提示 -->
        <VAlert
          v-if="error"
          type="error"
          variant="tonal"
          class="mb-4"
          closable
          @click:close="error = ''"
        >
          {{ error }}
        </VAlert>

        <VForm @submit.prevent="handleSubmit">
          <VTextField
            v-model="password"
            :type="showPassword ? 'text' : 'password'"
            label="请输入新密码"
            variant="outlined"
            class="password-field"
            :append-inner-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
            @click:append-inner="togglePasswordVisibility"
            :error="!!error && !password"
            hide-details="auto"
            autofocus
          />

          <div class="password-hint">
            <VIcon icon="mdi-information" size="small" class="me-1" />
            <span class="hint-text">密码长度至少4位</span>
          </div>
        </VForm>
      </VCardText>

      <VCardActions class="dialog-actions">
        <VSpacer />
        <VBtn
          variant="text"
          color="grey"
          @click="handleClose"
          :disabled="loading"
        >
          取消
        </VBtn>
        <VBtn
          variant="flat"
          color="primary"
          @click="handleSubmit"
          :loading="loading"
          :disabled="!isPasswordValid"
        >
          确认修改
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>

  <!-- 密码过期提示弹窗 -->
  <PasswordExpiredDialog v-model="showExpiredDialog" />
</template>

<style scoped>
.change-password-dialog :deep(.v-overlay__content) {
  margin: 24px;
}

.dialog-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.dialog-title {
  padding: 20px 24px 16px;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--v-theme-on-surface);
  border-bottom: 1px solid var(--v-theme-surface-variant);
}

.dialog-content {
  padding: 24px;
}

.password-field {
  margin-bottom: 8px;
}

.password-hint {
  display: flex;
  align-items: center;
  margin-top: 8px;
  margin-bottom: 16px;
}

.hint-text {
  font-size: 0.875rem;
  color: var(--v-theme-on-surface-variant);
}

.dialog-actions {
  padding: 16px 24px 20px;
  border-top: 1px solid var(--v-theme-surface-variant);
}

/* 暗色主题适配 */
.v-theme--dark .dialog-card {
  background-color: var(--v-theme-surface);
}

.v-theme--dark .dialog-title {
  border-bottom-color: rgba(255, 255, 255, 0.12);
}

.v-theme--dark .dialog-actions {
  border-top-color: rgba(255, 255, 255, 0.12);
}
</style>
